narwhals-2.0.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-2.0.1.dist-info/METADATA,sha256=zzmIkOIP4o2c_xJXFkNMF7FE5K8AYhaoOcllsUAJ2Xs,11260
narwhals-2.0.1.dist-info/RECORD,,
narwhals-2.0.1.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-2.0.1.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=UqPYDuUkCS8-xjfjXN-JAFr9Iy4__aosgmeKxjUQEEo,3224
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_constants.cpython-312.pyc,,
narwhals/__pycache__/_duration.cpython-312.pyc,,
narwhals/__pycache__/_enum.cpython-312.pyc,,
narwhals/__pycache__/_exceptions.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_namespace.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/_typing_compat.cpython-312.pyc,,
narwhals/__pycache__/_utils.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=QgbAGcl0WKRkxHf1K42r-aKddzesmzCsI9vL7K0ZhV8,28041
narwhals/_arrow/expr.py,sha256=fzEgEwVXETPfoxyvsI7fwTRGuh_t7BNCih0QP-fK4Io,6436
narwhals/_arrow/group_by.py,sha256=SkDRYpKaZXkwxtC-5s1yinBSgVgj2KoAiFFpjSvo9Fo,6458
narwhals/_arrow/namespace.py,sha256=kGQ6WM-JSTrejGEKrWIn7LQ8jTVYT8vYv1IDwhFiWYQ,12056
narwhals/_arrow/selectors.py,sha256=qIfCnMNlQ5svQzGaB-DV5YE4xSaUaVzElTPYJl_0BJc,1128
narwhals/_arrow/series.py,sha256=3ElpdbJRsU7fCPC9ORoim_But_fRQoN6Z4N72a_l8Hc,44535
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=zF87NGKJeauZ9jxkHKNs6PVKDxLlOGdk1KinEto01yU,8946
narwhals/_arrow/series_list.py,sha256=EpSul8DmTjQW00NQ5nLn9ZBSSUR0uuZ0IK6TLX1utwI,421
narwhals/_arrow/series_str.py,sha256=iouTrb8GkJJlZc2ImMLRt8Knh3SvuygVmqORrJc_FSA,3998
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=TmgG8eqF4uCRW5NFzWTiBvlUGvD46govtIC8gRyrkmA,2286
narwhals/_arrow/utils.py,sha256=xSIdsiPvc1ubZC1_-iZBqjadAV9g_V5PsTwrwUXWg3g,16538
narwhals/_compliant/__init__.py,sha256=fN7ey51bkGcJ2D3G-SmcEXQWA1nwt9LI2xEW4MYP1rY,2466
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/__pycache__/window.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=M-822dJoGwgfdCE9DsaIiH5YSQkzwIEeqbdarSPZKhA,3514
narwhals/_compliant/dataframe.py,sha256=dCT1sPs-1t8F7bfOZmxkEHQk7WxkjOPVkaqV8A8DLEs,17060
narwhals/_compliant/expr.py,sha256=lGOMkpcmX5ZXzb-jTUYoK6sCT4zs8SDduIK_9G-H5TI,40540
narwhals/_compliant/group_by.py,sha256=sHkhMOqD3lyxGyXbMCmREHMvFL4Fg12iChQdm10rilM,6705
narwhals/_compliant/namespace.py,sha256=-XR1GwKA4xu79FXaRV0xi1lDG_kIaK2SCHJIfDqcpko,7322
narwhals/_compliant/selectors.py,sha256=4UMULBdP3Sfw5PaTKpSVifnXeGFbialOq5qVar0cgyk,11561
narwhals/_compliant/series.py,sha256=8jES-eExC2__ePiJ9P3ah3qSZlvffazr7NeVBb0WKS8,17583
narwhals/_compliant/typing.py,sha256=qtWtyVOXwgbIE7rUf6O3CngklYJqVYp6CExh2qqM1iQ,7081
narwhals/_compliant/when_then.py,sha256=hY2O8dNYUaa-9OTUzYYfrzmQp0w13cEf0GtV1hKAiWs,4323
narwhals/_compliant/window.py,sha256=_ji4goVKkT4YPTyZa_I0N2yGmwBfB1_LDG0WSXGbmlo,505
narwhals/_constants.py,sha256=kE1KWsIky4ryabH-Z117ZtGW24ccTcreWOZJjpacO6I,1094
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=RR1lsnSdMiNwzgTk8ke_8ZiztidOkMISTDsFj-H9y8k,17287
narwhals/_dask/expr.py,sha256=2S7Z3n6X7Rfz3uLiNBtTmWsHb-AyE539UXbK0ReQHLo,25719
narwhals/_dask/expr_dt.py,sha256=8v2vulfu0Pk5oki4iLz8TxD2KInHSOjdHWUhNwgcX5c,6908
narwhals/_dask/expr_str.py,sha256=SrDcJq_3rHvx1jfQcfi07oS0SGnVkcLE6Xu3uPZfkuA,3558
narwhals/_dask/group_by.py,sha256=2-iqle5m6i5TC8nKRl7B1t3nsAJUXDsUurRQkMFovV4,4860
narwhals/_dask/namespace.py,sha256=exgEJAz_Dh4h2YrJPLWNjCywBGqEMnVhfr8zc4MtPSg,13481
narwhals/_dask/selectors.py,sha256=ZgUeKdeBz3q7iRRoqan3nABIh0me06Su7Go4VdEaxuI,1101
narwhals/_dask/utils.py,sha256=5awbPrqAG_vBgs-vydM6MBD2hMR9kb8Ojk_v0Fd8XJY,6536
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=muAwdfug9-6Z_40g3GBkb000lxPvsNVLqzYifor1zG0,19532
narwhals/_duckdb/expr.py,sha256=M5TcIO70eW_JocdkrKb4QfclNTIUYh7wCf_lFiKl6d4,11352
narwhals/_duckdb/expr_dt.py,sha256=BTR8AtFkNpZlIF43YIRLxHc_MYWUZZS6A0Ujsu0pwK0,5904
narwhals/_duckdb/expr_list.py,sha256=NSOiQqowuZXs1OctXmR2coBtwKlvh8kq6jQFeiZpjTs,496
narwhals/_duckdb/expr_str.py,sha256=Y_NGStrPSKAGNmj2pv9kQ9qGgjB8WPBuWrsN2TC4djY,4754
narwhals/_duckdb/expr_struct.py,sha256=eN06QA1JS6wjAt7_AZzW3xoztHM_hoadlFUl_hwsEiE,576
narwhals/_duckdb/group_by.py,sha256=nuueeiJYRcs31Ja973VvtLbWM2wnms0GYL7kAHDeju0,1123
narwhals/_duckdb/namespace.py,sha256=1HE7z1VraOi6NdJjXxQIAOkqCmxrhlkhqNRTyB1LTZw,5355
narwhals/_duckdb/selectors.py,sha256=yA16Z-MlJUJBjOu0XI9qVO4Zx7L_T5FN2DQqNAYhu-o,1033
narwhals/_duckdb/series.py,sha256=xBpuPUnSSIQ1vYEKjHQFZN7ix1ZyMwSchliDPpkf3Wk,1397
narwhals/_duckdb/typing.py,sha256=gO_Odyinkn4QZY_TU4uuzda6mbeo38glOOUUripcWgg,454
narwhals/_duckdb/utils.py,sha256=P6xr-CldjHyc2XmpB3Us-gY3p4Mi2yqOe9_yzZ1ADjs,14042
narwhals/_duration.py,sha256=WGzj3FVcC2KogqRhNeim3YDIwUn8HkXQHAljtvHrjwQ,3139
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_exceptions.py,sha256=OhT0MiQbcw_wE85Bl1YYZJjvtlX0rJMNUoZtKNCjTq8,1928
narwhals/_expression_parsing.py,sha256=tIqW9lvowHzWIQoNi21aLid2quJnX6E8p3PIGK584uE,23094
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_ibis/__pycache__/group_by.cpython-312.pyc,,
narwhals/_ibis/__pycache__/namespace.cpython-312.pyc,,
narwhals/_ibis/__pycache__/selectors.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/__pycache__/utils.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=322JsJ1jGTSLYwBXvpjIVXy4-NpIPgLONTntZkOs3y8,16098
narwhals/_ibis/expr.py,sha256=ChJ61LXou9HsiHqYiSSF-442mVGQSZ-717Iln16Hxkk,14003
narwhals/_ibis/expr_dt.py,sha256=4BmiVw2lRR7rbvOHDIPLHazDcHY-LAqjL_MYzKoslpA,4273
narwhals/_ibis/expr_list.py,sha256=CFsrJtcFPfx9UYZsHRWexNDTeajuntrJLOP4UaN2q54,437
narwhals/_ibis/expr_str.py,sha256=-RlnJ1N7b8ffXr-gmfXuhN6Y-LQxhXs9POEqRLVTCS8,5023
narwhals/_ibis/expr_struct.py,sha256=FDsa5MqcHhqPmpZIEfGBASdqxPkyImrlGTH7XUSw3cs,565
narwhals/_ibis/group_by.py,sha256=enNzAPUsA_LIwPNJ7jG_MJKyqG2HyCiesBEX3pJgJBg,1031
narwhals/_ibis/namespace.py,sha256=lpr_sAejoQwRKdac6Gjla_9pWYBQC2O_l4GP0LE_EuY,5412
narwhals/_ibis/selectors.py,sha256=SkFxoukpKc_OjwKoKHRm8VwMaphCMUeWBJ2g_oWz3D0,961
narwhals/_ibis/series.py,sha256=CZDwDPsdELKtdr7OWmcFyGqexr33Ucfnv_RU95VJxIQ,1218
narwhals/_ibis/utils.py,sha256=sPtzykwdtsnq7TQShTAVzKy2fVvFWETb_Lpx2A4hHc0,9309
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=GWlbo9OqzQh-Y-uevJ1Kr762oaFHqFJSc3ql00LDH9w,5921
narwhals/_interchange/series.py,sha256=nSxdlOZrw3wtavS42TMR_b_EGgPBv224ioZBMo5eoC8,1651
narwhals/_namespace.py,sha256=ZWX2L1Vivjcq50-E9JgZmwZ3s4DEdbAmNCSp5eViXcE,15434
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=XKNWa5YwUBJuxeOMNhcsdR_GMq3jq6uuBEXJq8CArF4,41572
narwhals/_pandas_like/expr.py,sha256=TH1R3ySx2MAAopASc6vW9wIa3DYIaVH7hBsM-Yn6kK8,14833
narwhals/_pandas_like/group_by.py,sha256=IZKdNLEPsJECmPgirSiFRBGN9YE0bpICTKyFv9yb7Z4,11707
narwhals/_pandas_like/namespace.py,sha256=cr4LxcfHSq13B_pKjnuE1PIvKrhmQtHWtgA_kIMwJFw,16854
narwhals/_pandas_like/selectors.py,sha256=Qf7r0H6R8cniwDwC2zlWxddsPx-AHFsZwDPQ9iCEiH8,1261
narwhals/_pandas_like/series.py,sha256=RDTVQIwaHIYS6_GWJmhX5y8G0kysww9M_-RA5TTySx8,42099
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=nV4LmocY1SawVsrYULyREbX2NPAMjb94L0dcGJJY8IQ,11625
narwhals/_pandas_like/series_list.py,sha256=mM2CB63Z8uLgpxVvbcIlfp18rDBRXvXK95vJ75Oj3dg,1109
narwhals/_pandas_like/series_str.py,sha256=r_iqLsVZt29ZqGKKcdHupqlror_C8VDU04twU48L3dc,3680
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=Awm2YnewvdA3l_4SEwb_5AithhwBYNx1t1ajaHnvUsM,1064
narwhals/_pandas_like/utils.py,sha256=Hs7S9nSdCKsJWs1ENh0PdsSLHDB3jtH63oZ8q4wqNyc,26069
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=_jYIR0h1m8KW4JXRnZQMNOSYKtNFNgssUWsRWPRV-UE,22045
narwhals/_polars/expr.py,sha256=r1iL1kN7aLyktu7cVn1ZFEhs_x0_F0y4x_p5wHp7qYs,16709
narwhals/_polars/group_by.py,sha256=v88hD-rOCNtCeT_YqMVII2V1c1B5TEwd0s6qOa1yXb4,2491
narwhals/_polars/namespace.py,sha256=rAJ3rvTjDYTS-zW8puOZ_No4Vw8BunAB3_0O8Gq_DuM,9949
narwhals/_polars/series.py,sha256=kl0PB_ASVPSCAKcMjD6SwIZ0K-iUAz27JfJcFohyCRo,26015
narwhals/_polars/typing.py,sha256=iBAA0Z0FT6vG4Zxn-Z9pCLcHnrkKtyIUAeM-mOxlBJU,655
narwhals/_polars/utils.py,sha256=E-0ZcoTHewmu0T4-m1QakqPIav8CV6Jbl_K9kkx9RUA,8602
narwhals/_spark_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=PCCT-qQ3SknJkX9mZYCPTN1vfeMhwtCcZArD8PmtQnw,21790
narwhals/_spark_like/expr.py,sha256=5koc7ieiOvawi_1xeIWIY55MkfjCx2AmkdYMwOvtN-M,14807
narwhals/_spark_like/expr_dt.py,sha256=w1C0njwHj8Y67r7_KTOKV6Eq_fN6fHfswuW9Xx-D_mo,8594
narwhals/_spark_like/expr_list.py,sha256=Z779VSDBT-gNrahr_IKrqiuhw709gR9SclctVBLSRbc,479
narwhals/_spark_like/expr_str.py,sha256=IVEdsueMJ-xKgi3ZLt1M3rLFwDMr1YkLTLuHq_0veSI,5739
narwhals/_spark_like/expr_struct.py,sha256=haBDpuRhn_nGAFjMF3arhhRr6NfefNei9vEmAOa0fQI,613
narwhals/_spark_like/group_by.py,sha256=rsAhSHEoA1pHzPk--9xtKvLJbTHOtJ45ftVKUhI7KUc,1246
narwhals/_spark_like/namespace.py,sha256=wbpP-w_zJMxJlnrG2NuOdP-sXYSEPxWU0FEwPoLr3PQ,7640
narwhals/_spark_like/selectors.py,sha256=SzJPoFjyIEviSSvPRvL81o1jjQJcM-Veqb52vFU66JQ,1086
narwhals/_spark_like/utils.py,sha256=sJIq-Kvh8zUUfYDxNKzXAspD3PQYMsXcUSjnxlPvVsI,11537
narwhals/_sql/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_sql/__pycache__/__init__.cpython-312.pyc,,
narwhals/_sql/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_sql/__pycache__/expr.cpython-312.pyc,,
narwhals/_sql/__pycache__/group_by.cpython-312.pyc,,
narwhals/_sql/__pycache__/namespace.cpython-312.pyc,,
narwhals/_sql/__pycache__/typing.cpython-312.pyc,,
narwhals/_sql/__pycache__/when_then.cpython-312.pyc,,
narwhals/_sql/dataframe.py,sha256=IhJuPm_ddH94IPrDup1rOlOhzj1nG0f9oUW6dP-aNfY,971
narwhals/_sql/expr.py,sha256=gCb29MB2vKrllbc-1nY04WKiCpnzoLJtu7eBcj-NT5k,27771
narwhals/_sql/group_by.py,sha256=34PG1zfg3ZM_o1sP71lILfVuDzYT_HirdPoUesbYI40,1598
narwhals/_sql/namespace.py,sha256=4z-q7VZyA4TvHcO7IDu3fruSRm4UvFVaXnu0wgzY_CQ,2816
narwhals/_sql/typing.py,sha256=e3LkLPI4oa2IzykR7BgO9IIfCKRw0vrX4uHxPTB-uJM,487
narwhals/_sql/when_then.py,sha256=4lXcc_J_N6vHGby6kPJl1PGqLPUGbgHYuIXiYROyoW4,3636
narwhals/_translate.py,sha256=e8RjNCNX4QGJWKjM6VANDTG_bVT2VusjNfjsnkCBO3g,6112
narwhals/_typing_compat.py,sha256=h-BtLEl7CrZ-hYLlwYTOcCdsUS3dvgvkxQTcDQ7RYmA,2516
narwhals/_utils.py,sha256=8nOPZdhf6-ffU9U6jyeuavwoZSKFdPkvjLvJgBfAAiY,65401
narwhals/dataframe.py,sha256=jo6whEMTnlitUwTCoKInn2pGaAuUJC5u_iJp_OSsvws,135853
narwhals/dependencies.py,sha256=C_dqrNq9GCN1gcv18PwF7ALIjF7sHiCvWLqYxUVLnRk,18766
narwhals/dtypes.py,sha256=-0MYWwHCuM-fYeeHk62pIkapQGeAkS6wrS1Qgs0mkFA,23360
narwhals/exceptions.py,sha256=9ocrbLNP7fZLqP2gV1PS9OexNhzf8h7of2wR5wi5kE0,3704
narwhals/expr.py,sha256=GfHSjiVIeI8RWbHWSiru8VNB2PbUkmB1wy2d5yLAwYo,97989
narwhals/expr_cat.py,sha256=ujxoF_OM-R1G-lGjeZGovltpLDlaoWPUpbowr2ZoYPs,1258
narwhals/expr_dt.py,sha256=4sg37zo_S-kfQ20K7X8ZHhxcxp3NNtbpOfwbi-2GBa8,33624
narwhals/expr_list.py,sha256=6x1m8n_WMfx68oAbKsuPl93Lah3Ily9E5bEpxpkwNbw,1769
narwhals/expr_name.py,sha256=p_4szUluC6ydwRbAVO34t9tf-no0nnUCCYNDArheoiE,5154
narwhals/expr_str.py,sha256=kO51GheSOsjUVsR_8Enf62qfOz4_2G4C-N-H5uIx1f4,20287
narwhals/expr_struct.py,sha256=GYD-Btem8zp5aFw2qDfkZntjx-Uzz_J-_GBT2b9bB4Y,1790
narwhals/functions.py,sha256=oHvRzfx8eJB-Y44iBusLMtdtH9n-hA_8tIxd6-rNEVE,64871
narwhals/group_by.py,sha256=4Hmiap6ri94owOWkps4ODQTbxMKKJUW56Hb_DEAgYJo,7258
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=ZtViq3RrAZ-HdEZnHrcb4QhIt4VgDlQVODfdusK2MCU,6157
narwhals/selectors.py,sha256=ybbFG7Sjebr8qoMgD43O6QuHBGl52yUpGRe08L1LKyo,10759
narwhals/series.py,sha256=iQUEf0E23XUHUjQ0W2homGBJULu8Iu5_jDcAbpzzVgo,90442
narwhals/series_cat.py,sha256=I5osb8Fj04iWqfEWjiyhVPiFYe3Kk_mTZXZjwn3jnRc,911
narwhals/series_dt.py,sha256=jLuDEc2ieyCiR30oIiUVRsfHZpm8kIFsowoLt8rGY10,25299
narwhals/series_list.py,sha256=NznN1Z50RSGX4uQBO4OBMtu7YBHRM58tgPKoJjmOrDg,1041
narwhals/series_str.py,sha256=rl8KlB5z_iGFGWNtsy3OxdkXZWfxOpVhhRkHIbqfmDw,16565
narwhals/series_struct.py,sha256=pmKigkmKe8m-40X9UWW5_8PLqNzHIKubElv2V2Ohu4I,974
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=NblYFj8kNm0zi3pkJSX-ZKKxW2Ao-gwfCWWFkrXu07o,41767
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=7zGmarnurUTgY6DI4KQ1MSAC7B9ZZiI5Em7plb-HAEs,2700
narwhals/stable/v1/_namespace.py,sha256=gfsbT4R4aLmmdArY35LRpEHPiUeZKEEnXGiY9ypFtwE,296
narwhals/stable/v1/dependencies.py,sha256=aM0IShF4hbaaMEDRJQXvsu4RABZOdBG4QhrpJPxb7fg,5001
narwhals/stable/v1/dtypes.py,sha256=u2NFDJyCkjsK6p3K9ULJS7CoG16z0Z1MQiACTVkhkH4,1082
narwhals/stable/v1/selectors.py,sha256=xEA9bBzkpTwUanGGoFwBCcHIAXb8alwrPX1mjzE9mDM,312
narwhals/stable/v1/typing.py,sha256=fmC1UUCsXapGUEUQYi0fHMpKe4x-SgzoZOXPDB4cGZQ,6112
narwhals/stable/v2/__init__.py,sha256=C64TVzmZ1RasnfsuFfxF_hMosqGVZvqKLUza0Yl4dbk,38204
narwhals/stable/v2/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v2/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v2/_namespace.py,sha256=oYB5nrFGxqqTonkRx9vUanyBxGs2Yb0j7_juMyvnvWA,296
narwhals/stable/v2/dependencies.py,sha256=vpYWx_dron6wFdbQ60G06EV2UJ_LMd52LDodCrAY5Jg,86
narwhals/stable/v2/dtypes.py,sha256=iMpk2Kc1mNiQYmboOSgmiAijklSUBHSHF2LTKMKnGe8,80
narwhals/stable/v2/selectors.py,sha256=sjJL3agHd8Rgf_lWhgCmEKruhWEkwHdX32-n85OqVJU,83
narwhals/stable/v2/typing.py,sha256=5A_ug36wr9ViG7kRfdjMY1b5zCc8jxQ65U7r3CHBazc,5782
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=uBGp3xYfof0z8ccPdqZoCFSw3e2Wq8H68LQ0-OcCTa0,24967
narwhals/typing.py,sha256=YYajUDHIrMaa1pFbshvWjPThanZDNN71mUenCg_kaXI,15334
narwhals/utils.py,sha256=2GT3XxucWI6l9r9jTwMw7Aha2G73FsSXgXNFZ3O_ZyA,223
